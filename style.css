/* 基础卡片样式 */
.card {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    line-height: 1.6;
}

/* 正面样式 */
.card-front {
    text-align: center;
}

.word-display {
    font-size: 2.5em;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 30px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

/* 正面各部分通用样式 */
.original-sentence-section,
.context-section,
.explanation-section {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-top: 20px;
}

.section-label {
    font-size: 1.1em;
    font-weight: 600;
    color: #34495e;
    margin-bottom: 15px;
    text-align: left;
}

.context-cloze {
    font-size: 1.3em;
    line-height: 1.8;
    color: #2c3e50;
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #3498db;
    margin: 15px 0;
    text-align: left;
}

/* 原始句子样式 */
.original-sentence {
    font-size: 1.2em;
    line-height: 1.8;
    color: #2c3e50;
    background: #f0f8ff;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #9b59b6;
    margin: 15px 0;
    text-align: left;
}

/* 解释说明样式 */
.explanation {
    font-size: 1.1em;
    line-height: 1.7;
    color: #2c3e50;
    background: #fff8dc;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #e67e22;
    margin: 15px 0;
    text-align: left;
}

.instruction {
    font-size: 0.9em;
    color: #7f8c8d;
    font-style: italic;
    margin-top: 15px;
    text-align: center;
}

/* 分割线 */
.divider {
    border: none;
    height: 2px;
    background: linear-gradient(to right, transparent, #bdc3c7, transparent);
    margin: 30px 0;
}

/* 背面样式 */
.card-back {
    margin-top: 20px;
}

.info-section {
    background: white;
    margin-bottom: 20px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0,0,0,0.08);
    transition: transform 0.2s ease;
}

.info-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.12);
}

.section-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    display: flex;
    align-items: center;
    font-weight: 600;
}

.section-header .icon {
    font-size: 1.2em;
    margin-right: 10px;
}

.section-header .title {
    font-size: 1.1em;
}

/* 各部分内容样式 */
.pronunciation {
    padding: 20px;
    font-size: 1.4em;
    font-weight: 500;
    color: #2c3e50;
    text-align: center;
    background: #f8f9fa;
}

.meanings {
    padding: 20px;
}

.meanings {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    line-height: 1.7;
    color: #2c3e50;
}

.word-family, .etymology, .synonyms, .examples {
    padding: 20px;
    color: #2c3e50;
    line-height: 1.7;
}

.etymology {
    background: #f0f8ff;
}

.synonyms {
    background: #fff5f5;
}

.examples {
    background: #f0fff4;
}

/* 移动端适配 */
@media (max-width: 480px) {
    .card {
        padding: 15px;
        margin: 10px;
    }
    
    .word-display {
        font-size: 2em;
    }
    
    .context-cloze,
    .original-sentence,
    .explanation {
        font-size: 1.1em;
        padding: 15px;
    }
    
    .section-header {
        padding: 12px 15px;
    }
    
    .pronunciation {
        font-size: 1.2em;
    }
}

/* 打印样式 */
@media print {
    .card {
        box-shadow: none;
        background: white;
    }
    
    .info-section {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ddd;
    }
}